import React, { useState, useEffect, use<PERSON>allback, useMemo } from 'react';
import { <PERSON><PERSON> } from "../../../ui/button";
import { Input } from "../../../ui/input";
import { Label } from "../../../ui/label";
import { ScrollArea } from "../../../ui/scroll-area";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../../ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "../../../ui/alert-dialog";
import {
  Save,
  Loader2,
  AlertCircle,
  Plus
} from 'lucide-react';
import { SavedParagraphMetadata, SavedParagraphCategory } from '../../../../../types/global';
import { useCreateBlockNote } from '@blocknote/react';
import { BlockNoteView } from '@blocknote/mantine';
import { useTheme } from '../../../../hooks/useTheme';
import { useNotification } from '../../../../contexts/NotificationContext';

// Import BlockNote CSS
import '@blocknote/core/fonts/inter.css';
import '@blocknote/mantine/style.css';

interface ParagraphFormProps {
  mode: 'create' | 'edit' | 'update';
  initialData?: SavedParagraphMetadata | null;
  initialContent?: any[] | null;
  categories: SavedParagraphCategory[];
  onSubmit: (data: {
    title: string;
    categoryId: string;
    content: any;
  }) => Promise<void>;
  onCancel: () => void;
  onOpenCategoryModal?: () => void;
}

export const ParagraphForm: React.FC<ParagraphFormProps> = ({
  mode,
  initialData,
  initialContent,
  categories,
  onSubmit,
  onCancel,
  onOpenCategoryModal
}) => {
  const [title, setTitle] = useState('');
  const [categoryId, setCategoryId] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [contentLoaded, setContentLoaded] = useState(false);
  const [contentError, setContentError] = useState<string | null>(null);
  const [showLeaveConfirmation, setShowLeaveConfirmation] = useState(false);

  // Get theme for BlockNote
  const { theme } = useTheme();

  // Get notification function
  const { showNotification } = useNotification();

  // Map theme to BlockNote theme prop
  const getBlockNoteTheme = useCallback(() => {
    switch (theme) {
      case 'light':
        return 'light';
      case 'dark':
        return 'dark';
      case 'custom':
        // Use dark as base theme, CSS overrides will handle custom styling
        return 'dark';
      default:
        return 'light';
    }
  }, [theme]);

  // Initialize form data
  useEffect(() => {
    if ((mode === 'edit' || mode === 'update') && initialData) {
      setTitle(initialData.title);
      setCategoryId(initialData.categoryId);
    }
    // For create mode, don't auto-select a category so placeholder shows
  }, [mode, initialData, categories]);

  // Show notification when in update mode
  useEffect(() => {
    if (mode === 'update' && contentLoaded) {
      showNotification('Remember, your highlighted text is copied. Paste it in to edit your saved paragraph!', 'info');
    }
  }, [mode, contentLoaded, showNotification]);

  // Create BlockNote editor once with empty content - content will be managed via useEffect
  const editor = useCreateBlockNote({
    initialContent: [{
      id: 'paragraph-1',
      type: 'paragraph',
      content: []
    }]
  });

  // Single effect to manage all content updates - optimized to prevent race conditions
  useEffect(() => {
    if (!editor) return;

    const loadContent = async () => {
      try {
        setContentError(null);

        if ((mode === 'edit' || mode === 'update') && initialData && !contentLoaded) {
          // Load content for edit/update mode
          const result = await window.fileStorage.getSavedParagraphById(initialData.id);
          const content = result?.content;

          if (content && Array.isArray(content) && content.length > 0) {
            editor.replaceBlocks(editor.document, content);
          }
          setContentLoaded(true);
        } else if (mode === 'create') {
          // Handle create mode content
          if (initialContent && initialContent.length > 0) {
            console.log('[ParagraphForm] Setting initial content:', initialContent);
            editor.replaceBlocks(editor.document, initialContent);
          } else {
            // Clear to empty state
            editor.replaceBlocks(editor.document, [{
              id: 'paragraph-1',
              type: 'paragraph',
              content: []
            }]);
          }
          setContentLoaded(true);
        }
      } catch (error) {
        console.error('Failed to load content:', error);
        setContentError('Failed to load content');
        setContentLoaded(true);
      }
    };

    loadContent();
  }, [editor, mode, initialData, initialContent, contentLoaded]);

  // Handle form submission
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    if (!editor || !title.trim()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const content = editor.document;

      // Validate content
      if (!content || content.length === 0) {
        throw new Error('Content cannot be empty');
      }

      await onSubmit({
        title: title.trim(),
        categoryId: categoryId || '', // Send empty string for no category
        content
      });
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  }, [editor, title, categoryId, onSubmit]);

  // Check if there's content that would be lost
  const hasUnsavedContent = useCallback(() => {
    if (!editor || (mode !== 'create' && mode !== 'update')) return false;

    const content = editor.document;
    if (!content || !Array.isArray(content)) return false;

    // Check if there are more than 1 block or if the single block has content
    if (content.length > 1) return true;

    // Check if the first block has any content
    const firstBlock = content[0];
    if (firstBlock && firstBlock.content && Array.isArray(firstBlock.content)) {
      return firstBlock.content.some((item: any) => {
        // Handle both text content and other inline content types
        if (item.text && item.text.trim().length > 0) return true;
        if (item.content && item.content.trim().length > 0) return true;
        return false;
      });
    }

    return false;
  }, [editor, mode]);

  // Handle cancel with confirmation if needed
  const handleCancel = useCallback(() => {
    if (hasUnsavedContent()) {
      setShowLeaveConfirmation(true);
    } else {
      onCancel();
    }
  }, [hasUnsavedContent, onCancel]);

  // Handle confirmed leave
  const handleConfirmedLeave = useCallback(() => {
    setShowLeaveConfirmation(false);
    onCancel();
  }, [onCancel]);

  // Get category options
  const categoryOptions = useMemo(() => {
    return categories.map(category => ({
      value: category.id,
      label: category.name,
      color: category.color
    }));
  }, [categories]);

  // Handle category selection including create new option
  const handleCategorySelect = useCallback((value: string) => {
    if (value === '__create_new__') {
      onOpenCategoryModal?.();
    } else {
      setCategoryId(value);
    }
  }, [onOpenCategoryModal]);

  if (!contentLoaded) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-6 w-6 animate-spin text-foreground hover:text-foreground/80 transition-colors" />
        <span className="ml-2 text-muted-foreground">Loading editor...</span>
      </div>
    );
  }

  if (contentError) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-primary-foreground hover:text-primary-foreground/80 transition-colors mx-auto mb-2" />
          <p className="text-foreground font-medium">Error Loading Content</p>
          <p className="text-muted-foreground text-sm mt-1">{contentError}</p>
          <Button
            variant="outline"
            size="sm"
            onClick={onCancel}
            className="mt-3"
          >
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="flex flex-col h-full">
      <ScrollArea className="flex-1 p-2">
        <div className="space-y-6 max-w-[98%] mx-auto">
          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title" className="text-sm font-medium text-foreground">
              Title *
            </Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter paragraph title..."
              required
              className="w-full center text-foreground"
            />
          </div>

          {/* Category */}
          <div className="space-y-2">
            <Label htmlFor="category" className="text-sm font-medium text-foreground">
              Category <span className="text-muted-foreground">(optional)</span>
            </Label>
            <Select value={categoryId} onValueChange={handleCategorySelect}>
              <SelectTrigger className="w-full text-foreground [&>svg]:text-foreground [&>svg]:opacity-100">
                <SelectValue placeholder="Select a category" className="text-foreground data-[placeholder]:text-foreground" />
              </SelectTrigger>
              <SelectContent className="custom-scrollbar">
                {categoryOptions.map(option => (
                  <SelectItem
                    key={option.value}
                    value={option.value}
                    className="py-3 cursor-pointer pl-3 hover:bg-muted/50 focus:bg-muted/50 hover:text-foreground focus:text-foreground"
                  >
                    <div className="flex items-center gap-3">
                      {/* Color indicator */}
                      <div
                        className="w-3 h-3 rounded-full border border-border/30"
                        style={{ backgroundColor: option.color || '#6B8DD6' }}
                      />
                      <span className="text-foreground">{option.label}</span>
                    </div>
                  </SelectItem>
                ))}

                {/* Create new category option */}
                {onOpenCategoryModal && (
                  <>
                    <div className="border-t border-border my-1" />
                    <SelectItem
                      value="__create_new__"
                      className="py-3 cursor-pointer pl-3 text-primary hover:bg-muted/50 focus:bg-muted/50 hover:text-primary focus:text-primary"
                    >
                      <div className="flex items-center gap-3">
                        <Plus className="w-3 h-3" />
                        <span className="font-medium">Create New Category</span>
                      </div>
                    </SelectItem>
                  </>
                )}
              </SelectContent>
            </Select>
          </div>

          {/* Content Editor */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-foreground">Content *</Label>
            <div className="border border-border rounded-md min-h-[300px] max-h-[400px] overflow-hidden">
              {editor && (
                <BlockNoteView
                  editor={editor}
                  theme={getBlockNoteTheme()}
                  className="blocknote-paragraph-editor p-3"
                />
              )}
            </div>
          </div>
        </div>
      </ScrollArea>

      {/* Actions */}
      <div className="flex justify-end gap-2 pt-4 border-t border-border">
        <Button
          type="button"
          variant="outline"
          onClick={handleCancel}
          disabled={isSubmitting}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting || !title.trim()}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2 text-foreground hover:text-foreground/80 transition-colors" />
              Saving...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2 hover:text-foreground/80 transition-colors" />
              {mode === 'create' ? 'Save Paragraph' : 'Update Paragraph'}
            </>
          )}
        </Button>
      </div>

      {/* Leave Confirmation Dialog */}
      <AlertDialog open={showLeaveConfirmation} onOpenChange={setShowLeaveConfirmation}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle
            className={"text-foreground"}
            >Discard Changes?</AlertDialogTitle>
            <AlertDialogDescription>
              You have unsaved content in the editor. If you leave now, your changes will be lost.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Stay</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmedLeave} className="bg-destructive hover:bg-destructive/90">
              Discard Changes
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </form>
  );
};
